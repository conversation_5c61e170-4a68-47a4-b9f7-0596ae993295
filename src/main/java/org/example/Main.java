package org.example;

import org.example.dao.BookDao;
import org.example.service.BookService;
import org.example.service.impl.BookServiceImpl;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

public class Main {
    public static void main(String[] args) {
//        //获取IOC容器
//        ApplicationContext c=new ClassPathXmlApplicationContext("applicationContext.xml");
//        //获取bean
//        BookDao bookDao = c.getBean(BookDao.class);
//        BookDao book = (BookDao) c.getBean("bookDao");        bookDao.save();
//        BookService bookService = c.getBean(BookService.class);
//        bookService.save();

        ApplicationContext c=new ClassPathXmlApplicationContext("applicationContext.xml");
        BookDao bookDao2 =(BookDao) c.getBean("bookDao2");
        BookDao bookDao =(BookDao) c.getBean("bookDao");
        bookDao.save();
        bookDao2.save();
        System.out.println(bookDao==bookDao2);
    }
}